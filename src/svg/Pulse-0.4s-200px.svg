<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="200px" height="200px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<rect x="22" y="23" width="6" height="54" fill="#000000">
  <animate attributeName="y" repeatCount="indefinite" dur="0.4255319148936171s" calcMode="spline" keyTimes="0;0.5;1" values="-4;23;23" keySplines="0 0.5 0.5 1;0 0.5 0.5 1" begin="-0.08510638297872342s"></animate>
  <animate attributeName="height" repeatCount="indefinite" dur="0.4255319148936171s" calcMode="spline" keyTimes="0;0.5;1" values="108;54;54" keySplines="0 0.5 0.5 1;0 0.5 0.5 1" begin="-0.08510638297872342s"></animate>
</rect>
<rect x="47" y="23" width="6" height="54" fill="#000000">
  <animate attributeName="y" repeatCount="indefinite" dur="0.4255319148936171s" calcMode="spline" keyTimes="0;0.5;1" values="2.75;23;23" keySplines="0 0.5 0.5 1;0 0.5 0.5 1" begin="-0.04255319148936171s"></animate>
  <animate attributeName="height" repeatCount="indefinite" dur="0.4255319148936171s" calcMode="spline" keyTimes="0;0.5;1" values="94.5;54;54" keySplines="0 0.5 0.5 1;0 0.5 0.5 1" begin="-0.04255319148936171s"></animate>
</rect>
<rect x="72" y="23" width="6" height="54" fill="#000000">
  <animate attributeName="y" repeatCount="indefinite" dur="0.4255319148936171s" calcMode="spline" keyTimes="0;0.5;1" values="2.75;23;23" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"></animate>
  <animate attributeName="height" repeatCount="indefinite" dur="0.4255319148936171s" calcMode="spline" keyTimes="0;0.5;1" values="94.5;54;54" keySplines="0 0.5 0.5 1;0 0.5 0.5 1"></animate>
</rect>
<!-- [ldio] generated by https://loading.io/ --></svg>