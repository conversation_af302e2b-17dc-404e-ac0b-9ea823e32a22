<!DOCTYPE html>
<html lang="en">
<!-- 


What are you doing here?! you sneaky developer...
-->

<head>
  <!-- don't forget to remove this >>>>(google tag manager) script, if you are using this page/code -->
  <!-- don't forget to remove this >>>>(google tag manager) script, if you are using this page/code -->
  <!-- don't forget to remove this >>>>(google tag manager) script, if you are using this page/code -->
  <!-- don't forget to remove this >>>>(google tag manager) script, if you are using this page/code -->
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-YMWDE0VTD3"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());

    gtag("config", "G-YMWDE0VTD3");
  </script>
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->
  <meta name="google-site-verification" content="zMNl24ruDnyqiliYwKNU_r09OkPCDKz0kMBtF9nqiCc" />
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->
  <!-- don't forget to remove this >>(google site verification meta tag) tag, if you are using this page/code -->

  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="robots" content="index, follow" />
  <meta name="theme-color" content="#000" />
  <meta name="description"
    content="Vinod Jangid | Portfolio, Visit and know about me & my projects/designs. I value simple content structure, clean design patterns, and thoughtful interactions." />
  <meta name="keywords"
    content="vinod jangid,vinod jangid portfolio,Vinod Jangid Portfolio,vinodjangid,vinod jangid site,Vinod Jangid Site,vinod jangid me,vinod jangid web,vinod jangid github,vinodjangid site,vinodjangid uiverse,uiverse,vinod jangid uiverse,vinod jangid frontend developer,frontend developer,vinod jangid jaipur,vinod jaipur" />
  <meta name="author" content="Vinod Jangid" />
  <meta property="og:image" content="https://www.vinodjangid.site/src/png/Preview-card-min.png" />
  <meta property="og:title" content="Vinod Jangid | Portfolio" />
  <meta property="og:description"
    content="Hi! My name is Vinod. I'm a web developer and digital visual artist. Visit and know about me & my projects/designs. I value simple content structure, clean design patterns, and thoughtful interactions." />

  <meta property="og:url" content="https://vinodjangid.site" />
  <meta property="og:site_name" content="Vinod Jangid Portfolio" />

  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:site" content="@Vinod_Jangid07" />
  <meta name="twitter:title" content="Vinod Jangid | Portfolio" />
  <meta name="twitter:description"
  content="Hi! My name is Vinod. I'm a web developer and digital visual artist. Visit and know about me & my projects/designs. I value simple content structure, clean design patterns, and thoughtful interactions." />
  <meta name="twitter:image" content="https://www.vinodjangid.site/src/png/Preview-card-min.png" />
  <title>Vinod Jangid | Portfolio</title>
  <link rel="icon" type="image/x-icon" href="src/png/main-favicon.png" />
  <!-- aos cdn -->
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
  <!-- stylesheet -->
  <link rel="stylesheet" href="style.css" />
  <!-- google fonts-->
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Passions+Conflict&family=Orbitron&family=Fira+Code&family=Six+Caps" />

<body class="body">
  <audio loop="" id="audioPlayer" style="display: none">
    <source src="src/mp3/preloader.mp3" type="audio/mp3" />
  </audio>
  <div id="preloader"></div>
  <noscript>Allow Javascript</noscript>

  <header>
    <div class="cursor-inner" id="cursor-inner"></div>
    <div class="cursor-outer" id="cursor-outer"></div>

    <div class="navbar" id="navbar">
      <div class="hey">Hey!</div>
      <div class="logo" tabindex="0" aria-label="vinod jangid logo">
        <div class="logo-top">
          <img src="src/png/nav-avatar.png" alt="animation-head" id="nav-avatar"/>
        </div>
      </div>
      <div class="navbar-tabs" id="navbar-tabs">
        <ul class="navbar-tabs-ul">
          <li class="home activeThistab" class="navbar-tabs-li" data-aos="fade-down" data-aos-delay="100">
            <a href="#home" tabindex="0" aria-label="Home menu button">
              &#60;/Home&#62;
            </a>
          </li>

          <li class="about" class="navbar-tabs-li" data-aos="fade-down" data-aos-delay="300">
            <a href="#about" aria-label="about menu button">
              &#60;/AboutMe&#62;
            </a>
          </li>

          <li class="experience" class="navbar-tabs-li" data-aos="fade-down" data-aos-delay="400">
            <a href="#experience" aria-label="experience menu button">
              &#60;/Experience&#62;
            </a>
          </li>

          <li class="skills" class="navbar-tabs-li" data-aos="fade-down" data-aos-delay="500">
            <a href="#skills" aria-label="skills menu button">
              &#60;/Skills&#62;
            </a>
          </li>

          <li class="projects" class="navbar-tabs-li" data-aos="fade-down" data-aos-delay="700">
            <a href="#projects" aria-label="projects menu button">
              &#60;/Projects&#62;
            </a>
          </li>
        </ul>
      </div>
      <!-- navbar tabs ends -->
    </div>
  </header>
  <!-- header ends here -->

  <main>
    <!-- hamburger -->
    <div class="hamburger" id="hamburger" data-aos="fade">
      <div class="hamburgerbase">
        <button id="hamburger-button" onclick="hamburgerMenu()" tabindex="0" aria-label="Menu Button">
          <span class="burger-bar" id="burger-bar1"></span>
          <span class="burger-bar" id="burger-bar2"></span>
          <span class="burger-bar" id="burger-bar3"></span>
        </button>
      </div>
    </div>
    <!-- hamburger -->
    <div class="mobiletogglemenu" id="mobiletogglemenu">
      <ul class="mobile-navbar-tabs-ul" id="mobile-ul">
        <li id="home-mobile-tab" class="mobile-navbar-tabs-li home activeThismobiletab" onclick="hidemenubyli()">
          <a href="#home" tabindex="0" aria-label="Home menu button">
            &#60;/Home&#62;
          </a>
        </li>

        <li id="aboutme-mobile-tab" class="mobile-navbar-tabs-li about" onclick="hidemenubyli()">
          <a href="#about" tabindex="0" aria-label="about menu button">
            &#60;/AboutMe&#62;
          </a>
        </li>

        <li id="experience-mobile-tab" class="mobile-navbar-tabs-li experience" onclick="hidemenubyli()">
          <a href="#experience" tabindex="0" aria-label="experience menu button">
            &#60;/Experience&#62;
          </a>
        </li>

        <li id="skills-mobile-tab" class="mobile-navbar-tabs-li skills" onclick="hidemenubyli()">
          <a href="#skills" tabindex="0" aria-label="skills menu button">
            &#60;/Skills&#62;
          </a>
        </li>

        <li id="projects-mobile-tab" class="mobile-navbar-tabs-li projects" onclick="hidemenubyli()">
          <a href="#projects" tabindex="0" aria-label="projects menu button">
            &#60;/Projects&#62;
          </a>
        </li>
      </ul>
    </div>
    <!-- mobile toggle menu ends -->

    <section class="landing-page-container" id="home">
      <div class="blob"></div>

      <div class="text-content">
        <article id="hello-friend" data-aos="fade-up" data-aos-delay="0">
          <p class="jello">H</p>
          <p class="jello">e</p>
          <p class="jello">l</p>
          <p class="jello">l</p>
          <p class="jello">o</p>
          <p class="jello">(</p>
          <p class="jello">)</p>
          <p class="jello">;</p>
          &nbsp;
          <p class="jello">I</p>
          <p class="jello">'</p>
          <p class="jello">m</p>
        </article>
        <article id="name" data-aos="fade-up" data-aos-delay="200">
          <p class="jello">S</p>
          <p class="jello">t</p>
          <p class="jello">a</p>
          <p class="jello">f</p>
          <p class="jello">n</p>
          <p class="jello">y</p>
          &nbsp;
          <!-- <p class="jello">J</p>
          <p class="jello">a</p>
          <p class="jello">n</p>
          <p class="jello">g</p>
          <p class="jello">i</p>
          <p class="jello">d</p>
          <p class="jello">.</p> -->
        </article>

        <article id="work" data-aos="fade-up" data-aos-delay="400">
          <div>
            <p class="jello">I</p>
          </div>
          <div>
            <p class="jello">d</p>
            <p class="jello">e</p>
            <p class="jello">s</p>
            <p class="jello">i</p>
            <p class="jello">g</p>
            <p class="jello">n</p>
          </div>
          <div>
            <p class="jello">&</p>
          </div>
          <div>
            <p class="jello">c</p>
            <p class="jello">o</p>
            <p class="jello">d</p>
            <p class="jello">e</p>
          </div>
          <div>
            <p class="jello">f</p>
            <p class="jello">o</p>
            <p class="jello">r</p>
          </div>
          <div>
            <p class="jello">w</p>
            <p class="jello">e</p>
            <p class="jello">b</p>
            <p class="jello">.</p>
          </div>
        </article>
        <p id="info-para" data-aos="fade-up" data-aos-delay="600">
          I specialize in crafting intuitive, playful web interfaces and love building products that blend functionality with delightful user experiences.<br />
          <br />
          Whether it’s boosting accessibility, scaling design systems, or mentoring teams, I bring thoughtful engineering and creative problem-solving to every project.
        </p>
        <div class="contact-btn-div" data-aos="fade-up" data-aos-delay="800">
          <a href="mailto:<EMAIL>" tabindex="-1">
            <button class="letsTalkBtn">
              <p class="letsTalkBtn-text">Let's Talk!</p>
              <span class="letsTalkBtn-BG"></span>
            </button>
          </a>
          <button class="resume-btn" id="resume-btn" onclick="openURL()">
            <div class="sign">
              <svg viewBox="0 0 640 512">
                <path d="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128H144zm79-167l80 80c9.4 9.4 24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-39 39V184c0-13.3-10.7-24-24-24s-24 10.7-24 24V318.1l-39-39c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9z" />
              </svg>
            </div>
            <div class="text">Resume</div>
          </button>
          <div class="setting-container" id="setting-container">
            <input type="checkbox" id="switchforsetting" onclick="settingtoggle()" />

            <label for="switchforsetting" class="needtobeinvert" id="labelforsetting" tabindex="0"
              aria-label="settings for sound and appearance"></label>

            <div class="visualmodetogglebuttoncontainer" id="visualmodetogglebuttoncontainer">
              <input type="checkbox" id="switchforvisualmode" onclick="visualmode()" />
              <label for="switchforvisualmode" id="labelforvisualmode" tabindex="0"
                aria-label="switch appearance"></label>
            </div>

            <div class="soundtogglebuttoncontainer" id="soundtogglebuttoncontainer">
              <input type="checkbox" id="switchforsound" onclick="playpause()" />
              <label for="switchforsound" id="labelforsound" tabindex="0" aria-label="switch sound"
                class="needtobeinvert"></label>
            </div>
          </div>
          <!-- setting div ends -->
        </div>
        <!-- contact-btn-div -->
      </div>
     
    </section>
    <!-- landing page ends here -->
    <section class="about-section-container" id="about" data-aos="fade-up">
      <div class="about-section">
        <div class="section-heading">
          <h2 class="section-heading-article" tabindex="0" aria-label="About me heading">
            &#60;/AboutMe&#62;
          </h2>
          <p class="sectionHeadingP"></p>
        </div>

        <div class="info-dp-section">
          <div class="about-info">
            <p tabindex="0">
              Hi! I'm Stafny — my love for computers started way back in 5th grade when I thought opening Paint was pure magic. By age 10, I designed my very first website and never looked back.
            </p>
            <br />
            <p tabindex="0">
              What began as childhood curiosity turned into a passion for designing fun, functional, and beautiful things for the web. I quickly realized the browser was my favorite canvas.
            </p>
            <br />
            <p tabindex="0">
              These days, I spend my time building creative and accessible web experiences. I love bringing ideas to life with clean code, playful interactions, and a touch of digital flair.
            </p>
            </div>

            <div class="dp" data-aos="fade-up" style="border-radius: 30px; overflow: hidden;">
            <img src="src/webp/stafny_dp.webp" alt="Stafny Karkada" tabindex="0" aria-label="image of stafny" style="border-radius: 30px;" />
            </div>
        </div>

        <div class="photo-grid" data-aos="fade-up">
          <div class="grid-item">
            <img src="src/webp/col_1.webp" alt="Profile Photo 1" />
          </div>
          <div class="grid-item wide">
            <img src="src/webp/col_3.webp" alt="Work Photo 1" />
          </div>
          <div class="grid-item tall">
            <img src="src/webp/col_2.webp" alt="Work Photo 2" />
          </div>
          <div class="grid-item">
            <img src="src/webp/col_3.webp" alt="Work Photo 3" />
          </div>
          <div class="grid-item large">
            <img src="src/webp/col_4.webp" alt="Project Photo 1" />
          </div>
          <div class="grid-item">
            <img src="src/webp/col_5.webp" alt="Project Photo 2" />
          </div>
        </div>
      </div>
    </section>
    <!-- about section ends  -->

    <section class="experience-section-container" id="experience" data-aos="fade-up">
      <div class="experience-section">
        <div class="section-heading">
          <h2 class="section-heading-article" tabindex="0" aria-label="Experience heading">
            &#60;/Experience&#62;
          </h2>
          <p class="sectionHeadingP"></p>
        </div>

        <div class="experience-items">
          <!-- Experience Item 1 -->
          <div class="experience-item" data-aos="fade-up">
            <div class="experience-content">
              <h3 class="company-name">Northeastern University</h3>
              <p class="job-title">Graduate Web Engineering Assistant</p>
              <ul class="job-responsibilities">
                <li>Delivered live coding sessions and workshops, mentoring 140+ students with real-time debugging support</li>
                <li>Guided 50+ student teams in developing multi-user applications with WebSockets and LLM integration</li>
                <li>Automated grading processes and implemented structured feedback systems to enhance learning</li>
                <li>Created comprehensive tutorial materials and code examples for advanced web development concepts</li>
              </ul>
            </div>
            <div class="company-image">
              <div class="image-container">
                <img src="src/webp/NorthEastern.webp" alt="Northeastern University" />
              </div>
            </div>
          </div>

          <!-- Experience Item 2 -->
          <div class="experience-item" data-aos="fade-up">
            <div class="experience-content">
              <h3 class="company-name">Autodesk</h3>
              <p class="job-title">Frontend Engineer Intern</p>
              <ul class="job-responsibilities">
                <li>Led comprehensive accessibility audit identifying 100+ issues using Axe DevTools and WAVE</li>
                <li>Improved Developer Portal performance by 60% through strategic UI/UX optimizations</li>
                <li>Reduced accessibility compliance issues by 80% through documentation and team workshops</li>
                <li>Implemented collaborative features enabling customer team creation and management</li>
              </ul>
            </div>
            <div class="company-image">
              <div class="image-container">
                <img src="src/webp/Autodesk.webp" alt="Autodesk" />
              </div>
            </div>
          </div>

          <!-- Experience Item 3 -->
          <div class="experience-item" data-aos="fade-up">
            <div class="experience-content">
              <h3 class="company-name">Y Media Labs</h3>
              <p class="job-title">Software Engineer</p>
              <ul class="job-responsibilities">
                <li>Optimized California Pizza Kitchen's website, reducing clicks by 75% and doubling conversions</li>
                <li>Built a modular React design system with Storybook, increasing development efficiency by 70%</li>
                <li>Developed white-labeled B2B solution for Clover supporting 20+ client customizations</li>
                <li>Created Next.js CMS website for WeTransfer, reducing time-to-market by 65%</li>
              </ul>
            </div>
            <div class="company-image">
              <div class="image-container">
                <img src="src/webp/yml.webp" alt="Y Media Labs" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="skills-section-container" id="skills">
      <div class="skills-section">
        <div class="section-heading" data-aos="fade-up">
          <h2 class="section-heading-article" tabindex="0" aria-label="skills heading">
            &#60;/Skills&#62;
          </h2>
          <p class="sectionHeadingP"></p>
        </div>

        <div class="frontend-dev-section">
          <h3 class="frontend-dev-heading" data-aos="fade-up" tabindex="0"
            aria-label="As a frontend a developer these are the skills i have">
            Tech Stack
          </h3>
          <ul class="tech-stack-wrapper">
            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/htmllogo.png" alt="Html skill" class="tech-stack-logo" />
              <span class="tooltip">HTML</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/csslogo.png" alt="css skill" class="tech-stack-logo" />
              <span class="tooltip">CSS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/jslogo.png" alt="js skill" class="tech-stack-logo" />
              <span class="tooltip">JS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/bootstraplogo.png" alt="bootstrap skill" class="tech-stack-logo" />
              <span class="tooltip">BOOTSTRAP</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/reactlogo.png" alt="react skill" class="tech-stack-logo" />
              <span class="tooltip">REACTJS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/nextlogo.png" alt="nextjs skill" class="tech-stack-logo" />
              <span class="tooltip">NEXTJS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/node.png" alt="nodejs skill" class="tech-stack-logo" />
              <span class="tooltip">NODEJS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/astro.png" alt="astro skill" class="tech-stack-logo" />
              <span class="tooltip">ASTRO</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/githublogo.png" alt="github skill" class="tech-stack-logo needtobeinvert" />
              <span class="tooltip">GITHUB</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/gitlogo.png" alt="git skill" class="tech-stack-logo" />
              <span class="tooltip">GIT</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/tinacms.png" alt="tinacms skill" class="tech-stack-logo" />
              <span class="tooltip">TINACMS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/contentfulcms.png" alt="contentful skill" class="tech-stack-logo" />
              <span class="tooltip">CONTENTFUL</span>
            </li>


            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/clogo.png" alt="c language skill" class="tech-stack-logo" />
              <span class="tooltip">C</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/cpplogo.png" alt="c++ language skill" class="tech-stack-logo" />
              <span class="tooltip">C++</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/pslogo.png" alt="photoshop skill" class="tech-stack-logo" />
              <span class="tooltip">ADOBE PS</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/figmalogo.png" alt="figma skill" class="tech-stack-logo" />
              <span class="tooltip">FIGMA</span>
            </li>

            <li class="tech-stack-box" data-aos="fade-up">
              <img src="./src/png/ailogo.png" alt="adobe illustrator skill" class="tech-stack-logo" />
              <span class="tooltip">ADOBE AI</span>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <!-- skills section ends -->
    <section class="projects-section-container" id="projects">
      <div class="projects-section-div">
        <div class="section-heading" data-aos="fade-up">
          <h2 class="section-heading-article" tabindex="0" aria-label="My projects starts from here">
            &#60;/Projects&#62;
          </h2>
          <p class="sectionHeadingP"></p>
        </div>
        <div class="project-boxes-div">
          <div data-aos="fade-up" class="project-box-wrapper">
            <div class="project-box project-box2" id="project-box2">
              <div class="info-div">
                <img src="src/webp/pexelicon-favicon.webp" alt="pexelicon website favicon" class="faviconforProject" />
                <article class="ProjectHeading">Pexelicons</article>
                <p class="ProjectDescription">
                  Pexelicons: A world of personalized file folder icons to
                  transform your PC into a reflection of your style.
                </p>
                <div class="project-buttons">
                  <a href="https://github.com/vinodjangid07/Pexelicons" target="_blank" class="github-redirect"
                    aria-label="Visit Pexelicons on GitHub">
                    <img src="src/svg/github.svg" alt="github redirect button" />
                  </a>
                  <a href="https://vinodjangid07.github.io/Pexelicons/" target="_blank" class="cta"
                    aria-label="Visit Pexelicons Live">
                    <span>Live view</span>
                    <svg viewBox="0 0 13 10" height="10px" width="15px">
                      <path d="M1,5 L11,5"></path>
                      <polyline points="8 1 12 5 8 9"></polyline>
                    </svg>
                  </a>
                </div>
              </div>
              <div class="image-div">
                <img src="src/webp/Group 76.webp" alt="Pexelicon website preview image" />
              </div>
            </div>
          </div>

          <div class="project-box-wrapper" data-aos="fade-up">
            <div class="project-box project-box5" id="project-box5">
              <div class="info-div">
                <img src="src/webp/cropped-1086600-192x192.webp" alt="comickaze website favicon"
                  class="faviconforProject" />
                <article class="ProjectHeading">ComicKaze</article>
                <p class="ProjectDescription">
                  ComicKaze is an ecommerce website built on wordpress by me
                  as a freelancer. It specializes in selling anime
                  merchandise.
                </p>
                <div class="project-buttons">
                  <a href="https://comickaze.in" target="_blank" class="cta" aria-label="Visit Comickaze live">
                    <span>Live view</span>
                    <svg viewBox="0 0 13 10" height="10px" width="15px">
                      <path d="M1,5 L11,5"></path>
                      <polyline points="8 1 12 5 8 9"></polyline>
                    </svg>
                  </a>
                </div>
              </div>
              <div class="image-div">
                <img src="src/webp/comickaze-preview.webp" alt="comickaze website preview image" />
              </div>
            </div>
          </div>

          <!-- ---------------------- -->
          <div class="project-box-wrapper" data-aos="fade-up">
            <div class="project-box project-box2" id="project-box2">
              <div class="info-div">
                <img src="src/svg/aquaregia-favicon.svg" alt="Aquaregia website favicon"
                  class="faviconforProjectAquaregia" />
                <article class="ProjectHeading">Aquaregia</article>
                <p class="ProjectDescription">
                  A website for University Maharaja College fest "Aquaregia"
                  for event registrations.
                </p>
                <div class="project-buttons">
                  <a href="https://github.com/vinodjangid07/Aquaregia" target="_blank" class="github-redirect"
                    aria-label="Visit Aquaregia on GitHub">
                    <img src="src/svg/github.svg" alt="github redirect button" />
                  </a>
                  <a href="https://vinodjangid07.github.io/Aquaregia/" target="_blank" class="cta"
                    aria-label="Visit Aquaregia Live">
                    <span>Live view</span>
                    <svg viewBox="0 0 13 10" height="10px" width="15px">
                      <path d="M1,5 L11,5"></path>
                      <polyline points="8 1 12 5 8 9"></polyline>
                    </svg>
                  </a>
                </div>
              </div>
              <div class="image-div">
                <img src="src/webp/Aquaregia-project.webp" alt="Aquaregia website preview image" />
              </div>
            </div>
          </div>
          <!-- ---------------------- -->
          <div class="project-box-wrapper" data-aos="fade-up">
            <div class="project-box project-box4" id="project-box4">
              <div class="info-div">
                <img src="src/webp/qr-favicon.webp" alt="Qr generator website favicon" class="faviconforProject" />
                <article class="ProjectHeading">QR Generator</article>
                <p class="ProjectDescription">
                  A webpage to make a QR Code from text using API.
                </p>
                <div class="project-buttons">
                  <a href="https://github.com/vinodjangid07/QR-Generator" target="_blank" class="github-redirect"
                    aria-label="Visit qr generator on github">
                    <img src="src/svg/github.svg" alt="github redirect button" />
                  </a>
                  <a href="https://vinodjangid07.github.io/QR-Generator/" target="_blank" class="cta"
                    aria-label="Visit qr generator live">
                    <span>Live view</span>
                    <svg viewBox="0 0 13 10" height="10px" width="15px">
                      <path d="M1,5 L11,5"></path>
                      <polyline points="8 1 12 5 8 9"></polyline>
                    </svg>
                  </a>
                </div>
              </div>
              <div class="image-div">
                <img src="src/webp/QRgeneratorProject.webp" alt="qr generator website preview image" />
              </div>
            </div>
          </div>

          <!-- ---------------------- -->
          <div class="project-box-wrapper" data-aos="fade-up">
            <div class="project-box project-box3" id="project-box3">
              <div class="info-div">
                <img src="src/ico/netflixfavicon.ico" alt="netflix website favicon" class="faviconforProject" />
                <article class="ProjectHeading">React Netflix Clone</article>
                <p class="ProjectDescription">
                  A webpage to categorize the shows to redirect user to watch
                  the show.
                </p>
                <div class="project-buttons">
                  <a href="https://github.com/vinodjangid07/Netflix-clone" target="_blank" class="github-redirect"
                    aria-label="Visit Netflix clone on GitHub">
                    <img src="src/svg/github.svg" alt="github redirect button" />
                  </a>
                  <a href="https://react-project-20.netlify.app/" target="_blank" class="cta"
                    aria-label="Visit netflix clone live">
                    <span>Live view</span>
                    <svg viewBox="0 0 13 10" height="10px" width="15px">
                      <path d="M1,5 L11,5"></path>
                      <polyline points="8 1 12 5 8 9"></polyline>
                    </svg>
                  </a>
                </div>
              </div>
              <div class="image-div">
                <img src="src/webp/NetflixCloneProject.webp" alt="netflix clone website preview image" />
              </div>
            </div>
          </div>

          <!-- ---------------------- -->

          <div class="project-box-wrapper" data-aos="fade-up">
            <div class="project-box project-box1" id="project-box1">
              <div class="info-div">
                <img src="src/webp/axocean-favicon.webp" alt="Axocean website favicon" class="faviconforProject" />
                <article class="ProjectHeading">AXOCEAN</article>
                <p class="ProjectDescription">
                  E-commerce website frontend work to choose people their
                  favorite design of product and redirect to flipkart shopping
                  site.
                </p>
                <div class="project-buttons">
                  <a href="https://github.com/vinodjangid07/AXOCEAN" target="_blank" class="github-redirect"
                    aria-label="Visit axocean on GitHub">
                    <img src="src/svg/github.svg" alt="github redirect button" />
                  </a>
                  <a href="https://vinodjangid07.github.io/AXOCEAN/" target="_blank" class="cta"
                    aria-label="Visit axocean live">
                    <span>Live view</span>
                    <svg viewBox="0 0 13 10" height="10px" width="15px">
                      <path d="M1,5 L11,5"></path>
                      <polyline points="8 1 12 5 8 9"></polyline>
                    </svg>
                  </a>
                </div>
              </div>
              <div class="image-div">
                <img src="src/webp/AXOCEAN-project.webp" alt="Axocean website preview image" />
              </div>
            </div>
          </div>
          <!-- ---------------------- -->
        </div>
      </div>
    </section>
  </main>
  <!-- main ends here -->

  <footer id="footer">
    <button class="fas" id="backtotopbutton" onclick="scrolltoTopfunction()">
      <article aria-label="Back to top">&#8592;BACK TO TOP</article>
    </button>
    <div class="footer-background">
      <div class="footer-blob"></div>
    </div>
    <div class="footer-foreground">
      <div class="footercontainer">
        <div class="two-words">
          <article tabindex="0" aria-label="Learning, Living, and Leveling Up, my quote">
            "Learning, Living, and Leveling Up."
          </article>
        </div>
        <div class="social-media-container">
          <div class="getintouch-heading">
            <article>GetinTouch();</article>
          </div>
          <div class="logos">
            <a href="https://www.instagram.com/pexel_ui/" target="_blank" tabindex="0"
              aria-label="My instagram"><svg viewBox="0 0 448 512" class="SocialHandle">
                <path
                  d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" />
              </svg></a>

            <a href="https://www.linkedin.com/in/vinod-jangid-b401111a1?original_referer=" target="_blank"
              aria-label="My Linkdin"><svg class="SocialHandle" viewBox="0 0 448 512">
                <path
                  d="M100.28 448H7.4V148.9h92.88zM53.79 108.1C24.09 108.1 0 83.5 0 53.8a53.79 53.79 0 0 1 107.58 0c0 29.7-24.1 54.3-53.79 54.3zM447.9 448h-92.68V302.4c0-34.7-.7-79.2-48.29-79.2-48.29 0-55.69 37.7-55.69 76.7V448h-92.78V148.9h89.08v40.8h1.3c12.4-23.5 42.69-48.3 87.88-48.3 94 0 111.28 61.9 111.28 142.3V448z" />
              </svg></a>

            <a href="https://github.com/vinodjangid07" target="_blank" aria-label="My github"><svg viewBox="0 0 496 512"
                class="SocialHandle">
                <path
                  d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z" />
              </svg></a>

            <a href="mailto:<EMAIL>" target="_blank" aria-label="My G mail"><svg id="GmailLogo"
                viewBox="0 0 512 512">
                <path
                  d="M64 112c-8.8 0-16 7.2-16 16v22.1L220.5 291.7c20.7 17 50.4 17 71.1 0L464 150.1V128c0-8.8-7.2-16-16-16H64zM48 212.2V384c0 8.8 7.2 16 16 16H448c8.8 0 16-7.2 16-16V212.2L322 328.8c-38.4 31.5-93.7 31.5-132 0L48 212.2zM0 128C0 92.7 28.7 64 64 64H448c35.3 0 64 28.7 64 64V384c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128z" />
              </svg></a>
          </div>
        </div>
        <div class="footer-avatar-container">
          <img src="src/png/footer-avatar-vinod.png" alt="animation-head" class="footer-avatar-img" id="footer-wala-avatar"/>
          <div class="footer-avatar-face">
            <div class="footer-avatar-eye footer-left-eye">
              <div class="footer-pupil"></div>
            </div>
            <div class="footer-avatar-eye footer-right-eye">
              <div class="footer-pupil"></div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <article>
            Design & Built by Vinod Jangid
            &#169; twentytwentythree
          </article>
        </div>
      </div>
    </div>
  </footer>
  <!-- footer ends here -->

  <script src="main.js"></script>

  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
  <script>
    AOS.init();
  </script>

  <script>
    function openURL() {
      var url = "src/pdf/Vinod's Resume.pdf";

      window.open(url, "_blank");
    }
  </script>

  <script>
    const cursorInner = document.getElementById("cursor-inner");
    const cursorOuter = document.getElementById("cursor-outer");
    const links = document.querySelectorAll("a,label,button");

    document.addEventListener("mousemove", function (e) {
      const posX = e.clientX;
      const posY = e.clientY;

      cursorInner.style.left = `${posX}px`;
      cursorInner.style.top = `${posY}px`;

      // cursorOuter.style.left = `${posX}px`;
      // cursorOuter.style.top = `${posY}px`;

      cursorOuter.animate(
        {
          left: `${posX}px`,
          top: `${posY}px`,
        },
        { duration: 500, fill: "forwards" }
      );

      links.forEach((link) => {
        link.addEventListener("mouseenter", () => {
          cursorInner.classList.add("hover");
          cursorOuter.classList.add("hover");
        });
        link.addEventListener("mouseleave", () => {
          cursorInner.classList.remove("hover");
          cursorOuter.classList.remove("hover");
        });
      });
    });
  </script>
 <script src='https://storage.ko-fi.com/cdn/scripts/overlay-widget.js'></script>
<script>
  kofiWidgetOverlay.draw('vinodjangid07', {
    'type': 'floating-chat',
    'floating-chat.donateButton.text': 'Support me',
    'floating-chat.donateButton.background-color': '#794bc4',
    'floating-chat.donateButton.text-color': '#fff'
  });
</script>
</body>

</html>