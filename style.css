#labelforsetting,
#labelforvisualmode,
#switchforvisualmode:checked+#labelforvisualmode {
    background-repeat: no-repeat;
    background-position: center
}

#labelforsetting,
#labelforsound {
    height: 40px;
    background-size: 60%
}

#preloader,
.invertapplied,
.invertsettinglabel {
    filter: invert(1)
}

#preloader,
noscript {
    width: 100%;
    height: 100%
}

body,
noscript {
    background-color: var(--color-dark-mode)
}

.main,
body {
    overflow-x: hidden
}

#backtotopbutton article,
:root {
    font-family: Whitney, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif
}

.eye,
.image-div,
.logo,
.navbar,
.project-box,
.resume-btn,
footer {
    overflow: hidden
}

.cta,
.github-redirect,
.navbar-tabs-ul a {
    text-decoration: none
}

#GmailLogo,
.sign svg path {
    fill: white
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent
}

::selection {
    background-color: transparent
}

::-moz-selection {
    background: 0 0
}

html {
    font-size: 62.5%;
    scroll-behavior: smooth;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased
}

:root {
    cursor: default;
    --color-white: #fff;
    --color-black: #000;
    --color-dark-mode: #11111b;
    --color-light-mode: #ece7e1;
    --color-ddd-color: #ddd;
    --color-gray: #c4c4c4;
    --color-purple: #8000ff;
    --color-light-purple: #cf59e6;
    --color-light-blue: #6bc5f8;
    --color-navbarBorder-dark: #e2e2e207;
    --color-shadowDark: rgba(0, 0, 0, 0.137);
    --static-heading-gradient-blue: #b0f3f1;
    --static-heading-gradient-pink: #ffcfdf;
    --tech-stack-box-first-color: #1a1a29;
    --tech-stack-box-second-color: rgba(27, 27, 40, 0);
    --tech-stack-box-border-color: #292929
}

#hello-friend,
.footer-bottom article,
.getintouch-heading article,
.language-speak article,
.navbar-tabs-ul li,
.two-words article {
    font-family: fira code
}

::-webkit-scrollbar {
    width: 5px
}

::-webkit-scrollbar-track {
    background: #0a0a10
}

::-webkit-scrollbar-thumb {
    background-color: #8000ff
}

.cursor-inner {
    width: 8px;
    height: 8px;
    background-color: var(--color-light-blue)
}

.cursor-outer {
    width: 35px;
    height: 35px;
    border: 2px solid #fff
}

.cursor-inner,
.cursor-outer {
    position: fixed;
    top: 0;
    left: 0;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    z-index: 9999;
    pointer-events: none
}

.setting-container,
.settingactivate {
    transform-origin: left;
    transition-duration: .5s
}

.cursor-inner.hover {
    width: 25px;
    height: 25px;
    transition: .2s;
    mix-blend-mode: difference
}

.cursor-outer.hover {
    width: 50px;
    height: 50px;
    transition: .2s
}

.activeThistab:before,
.navbar-tabs-ul li:hover:before {
    background-color: var(--color-white);
    height: 10px;
    width: 10px;
    left: -20px
}

.light-mode {
    --color-dark-mode: #ece7e1;
    --color-black: #fff;
    --color-white: #000;
    --color-ddd-color: rgb(31, 31, 31);
    --color-navbarBorder-dark: #d1d6eb;
    --static-heading-gradient-blue: #0f7878;
    --static-heading-gradient-pink: #f09bb9;
    --color-shadowDark: #e2ddd7;
    --tech-stack-box-first-color: #e7e2db;
    --tech-stack-box-second-color: #ece7e100;
    --tech-stack-box-border-color: #dad5cf
}

.setting-container {
    width: 60px;
    height: 50px;
    margin-left: 50px;
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 20px 0
}

#labelforsetting {
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    background-image: url(src/svg/settings-svgrepo-com.svg);
    transition-duration: .5s;
    cursor: pointer
}

.soundtogglebuttoncontainer,
.visualmodetogglebuttoncontainer {
    align-items: center;
    width: fit-content;
    transition-duration: .3s
}

.settingactivate {
    width: 200px
}

.visualmodetogglebuttoncontainer {
    height: fit-content;
    display: flex;
    justify-content: center;
    transform: translate(-50px) scale(0)
}

.visualmodeshow {
    transition-duration: .3s;
    transform: translate(0) scale(1)
}

#switchforsetting,
#switchforsound,
#switchforvisualmode,
.hamburger,
.mobiletogglemenu {
    display: none
}

#switchforsetting:checked+#labelforsetting {
    transform: rotate(180deg);
    transition-duration: .5s
}

#labelforvisualmode {
    position: relative;
    width: 40px;
    height: 40px;
    background-image: url(src/png/crescent-moon\ \(1\).png);
    background-size: 50%;
    transition-duration: .5s;
    cursor: pointer
}

#switchforvisualmode:checked+#labelforvisualmode {
    transform: rotate(360deg);
    transition-duration: .5s;
    background-image: url(src/png/sun.png);
    background-size: 60%
}

.soundtogglebuttoncontainer {
    height: fit-content;
    display: flex;
    justify-content: center;
    transform: translate(-130px) scale(0)
}

#labelforsound,
#preloader {
    display: flex;
    align-items: center;
    background-repeat: no-repeat;
    background-position: center
}

.soundmodeshow {
    transition-delay: .1s;
    transition-duration: .5s;
    display: inline-block;
    transform: translate(0) scale(1)
}

.logo,
.logo:hover {
    transition-duration: 1s
}

#labelforsound {
    position: relative;
    justify-content: center;
    width: 40px;
    background-image: url(src/svg/Mute_Icon.svg);
    cursor: pointer;
    transition-duration: .5s
}

#switchforsound:checked+#labelforsound {
    transition-duration: .5s;
    background-image: url(src/svg/Speaker_Icon.svg);
    background-size: 50%;
    background-repeat: no-repeat;
    background-position: center
}

header {
    width: 100%;
    height: 115px;
    display: flex;
    align-items: flex-end;
    justify-content: center
}

#preloader {
    background-color: #fff;
    position: fixed;
    z-index: 99999;
    justify-content: center;
    overflow-y: hidden;
    background-image: url(src/svg/Pulse-0.4s-200px.svg);
    background-size: 10%
}

.navbar,
noscript {
    display: flex;
    align-items: center;
    position: fixed
}

#name,
.getintouch-heading article {
    background: -webkit-linear-gradient(135deg, var(--color-light-blue), var(--color-light-purple), var(--color-light-blue), var(--color-light-purple));
    -webkit-text-fill-color: transparent
}

noscript {
    justify-content: center;
    font-size: 4rem;
    color: #fff;
    z-index: 999999
}

.fakenavbar {
    height: 0;
    width: 0%;
    background-color: transparent
}

.navbar {
    background-image: linear-gradient(to bottom right, var(--tech-stack-box-first-color), var(--tech-stack-box-second-color));
    backdrop-filter: blur(10px);
    height: 80px;
    width: 80%;
    padding: 0 20px;
    justify-content: space-between;
    z-index: 999;
    border: 1px solid var(--color-navbarBorder-dark);
    border-radius: 50px
}

.navbar-tabs,
.navbar-tabs-ul {
    height: 100%;
    align-items: center
}

.logo,
.navbar-tabs-ul li {
    position: relative;
    display: flex
}

.navbar-tabs {
    display: flex;
    width: 80%
}

.navbar-tabs-ul {
    width: 100%;
    display: flex;
    list-style: none;
    justify-content: flex-end;
    gap: 80px;
    color: var(--color-white)
}

.navbar-tabs-ul li {
    width: fit-content;
    height: 30px;
    text-align: center;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem
}

.navbar-tabs-ul li:hover:before {
    content: "";
    position: absolute;
    border-radius: 5px;
    z-index: -1
}

.navbar-tabs-ul a {
    color: var(--color-white);
    font-weight: 100
}

.logo {
    width: 10%;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.face,
.logo-top {
    width: 100%;
    align-items: center;
    display: flex
}

.hey {
    color: #fff;
    position: absolute;
    font-size: 2.5rem;
    font-weight: 300;
    background-color: #20222e;
    padding: 5px 10px;
    border-radius: 20px;
    left: 120px;
    bottom: 80px;
    opacity: 0
}

#hello-friend,
#work,
.about-info p,
.activeThistab,
.language-speak article {
    color: var(--color-white)
}

.blob,
.eye,
.pupil {
    border-radius: 50%
}

.popup {
    animation: 3s linear pop-up
}

@keyframes pop-up {
    from {
        bottom: -50px;
        left: 50px;
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.logo-top {
    height: 100%;
    justify-content: center;
    transform-origin: bottom;
    position: relative
}

.activeThistab:before,
.face {
    content: "";
    position: absolute
}

.logo-top img {
    height: 100%;
    z-index: 2
}

.face {
    height: 74%;
    bottom: 0;
    justify-content: center;
    gap: 5px;
    z-index: 1;
    padding-left: 1px
}

.eye {
    width: 8px;
    height: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff
}

.left-eye {
    box-shadow: -1px -2px 2px rgba(0, 0, 0, .589) inset
}

.right-eye {
    box-shadow: 1px 3px 2px rgba(0, 0, 0, .589) inset
}

.pupil {
    width: 3.5px;
    height: 3.5px;
    background: #241010
}

.logo:hover {
    transform: translateY(200px)
}

.activeThistab:before {
    border-radius: 50%;
    z-index: -1
}

.dp::after,
.dp::before {
    height: 300px;
    z-index: -2;
    content: ""
}

.main {
    width: 100%;
    height: fit-content;
    position: relative
}

.blob {
    position: absolute;
    right: -15%;
    top: 0;
    background-color: var(--color-light-purple);
    width: 600px;
    height: 100vh;
    filter: blur(300px);
    opacity: .3;
    animation: 1s linear infinite alternate-reverse breath
}

@keyframes breath {
    from {
        opacity: .3
    }

    to {
        opacity: .5
    }
}

.landing-page-container {
    width: 100%;
    height: calc(100vh - 115px);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

.text-content {
    display: flex;
    flex-direction: column;
    width: 60%;
    height: 100%;
    align-items: flex-start;
    justify-content: center
}

#hello-friend {
    font-size: 3.5rem;
    font-weight: 600;
    width: fit-content;
    display: flex;
    align-items: flex-end
}

#name,
#work {
    font-size: 8rem;
    width: fit-content
}

#name,
#work,
.letsTalkBtn-text {
    display: flex;
    font-weight: 700
}

@-webkit-keyframes vibrate-1 {

    0%,
    100% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }

    20% {
        -webkit-transform: translate(-2px, 2px);
        transform: translate(-2px, 2px)
    }

    40% {
        -webkit-transform: translate(-2px, -2px);
        transform: translate(-2px, -2px)
    }

    60% {
        -webkit-transform: translate(2px, 2px);
        transform: translate(2px, 2px)
    }

    80% {
        -webkit-transform: translate(2px, -2px);
        transform: translate(2px, -2px)
    }
}

@keyframes vibrate-1 {

    0%,
    100% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }

    20% {
        -webkit-transform: translate(-2px, 2px);
        transform: translate(-2px, 2px)
    }

    40% {
        -webkit-transform: translate(-2px, -2px);
        transform: translate(-2px, -2px)
    }

    60% {
        -webkit-transform: translate(2px, 2px);
        transform: translate(2px, 2px)
    }

    80% {
        -webkit-transform: translate(2px, -2px);
        transform: translate(2px, -2px)
    }
}

#name {
    align-items: flex-start;
    background: linear-gradient(-45deg, var(--color-light-blue), var(--color-light-purple), var(--color-light-blue), var(--color-light-purple));
    background-clip: text;
    -webkit-background-clip: text;
    background-size: 400% 400%;
    -webkit-animation: 3s infinite gradient;
    animation: 3s infinite gradient
}

@keyframes gradient {

    0%,
    100% {
        background-position: 0 50%
    }

    50% {
        background-position: 100% 50%
    }
}

#work {
    align-items: flex-start;
    flex-wrap: wrap
}

#info-para,
.about-info p {
    font-weight: 400;
    font-size: 2rem
}

#work div {
    display: flex;
    margin: 0 12px 0 0
}

.jello:hover {
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-color: #03e6ff;
    -webkit-animation: .9s both jello-vertical;
    animation: .9s both jello-vertical
}

#backtotopbutton,
.dp::after,
.dp::before,
.letsTalkBtn {
    background-color: transparent
}

@-webkit-keyframes jello-vertical {

    0%,
    100% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    30% {
        -webkit-transform: scale3d(.75, 1.25, 1);
        transform: scale3d(.75, 1.25, 1)
    }

    40% {
        -webkit-transform: scale3d(1.25, .75, 1);
        transform: scale3d(1.25, .75, 1)
    }

    50% {
        -webkit-transform: scale3d(.85, 1.15, 1);
        transform: scale3d(.85, 1.15, 1)
    }

    65% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1)
    }

    75% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1)
    }
}

@keyframes jello-vertical {

    0%,
    100% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1)
    }

    30% {
        -webkit-transform: scale3d(.75, 1.25, 1);
        transform: scale3d(.75, 1.25, 1)
    }

    40% {
        -webkit-transform: scale3d(1.25, .75, 1);
        transform: scale3d(1.25, .75, 1)
    }

    50% {
        -webkit-transform: scale3d(.85, 1.15, 1);
        transform: scale3d(.85, 1.15, 1)
    }

    65% {
        -webkit-transform: scale3d(1.05, .95, 1);
        transform: scale3d(1.05, .95, 1)
    }

    75% {
        -webkit-transform: scale3d(.95, 1.05, 1);
        transform: scale3d(.95, 1.05, 1)
    }
}

#info-para {
    width: 75%;
    padding-top: 30px;
    color: var(--color-ddd-color);
    line-height: 20px
}

.contact-btn-div {
    width: 100%;
    padding-top: 50px;
    height: fit-content;
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Photo Grid Styles */
.photo-grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-auto-rows: 200px;
    grid-auto-flow: dense;
    gap: 20px;
    margin-top: 60px;
    padding: 0;
}

.grid-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.grid-item:hover {
    transform: scale(1.02);
}

.grid-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.grid-item.wide {
    grid-column: span 2;
}

.grid-item.tall {
    grid-row: span 2;
}

.grid-item.large {
    grid-column: span 2;
    grid-row: span 2;
}

@media screen and (max-width: 768px) {
    .contact-btn-div {
        flex-direction: column;
    }
    
    .photo-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        grid-auto-rows: 120px;
        gap: 10px;
    }
}

.letsTalkBtn {
    position: relative;
    border: none;
    width: 180px;
    height: 40px;
    border-radius: 0;
    cursor: pointer;
    transition: .3s
}

.letsTalkBtn-text {
    width: 100%;
    height: 100%;
    background-color: rgba(230, 230, 230, .466);
    border: none;
    border-radius: 7px;
    backdrop-filter: blur(5px);
    color: #000;
    align-items: center;
    justify-content: center;
    letter-spacing: .8px
}

.cta:hover span,
.text,
.tooltip {
    color: #fff
}

.resume-btn,
.sign {
    align-items: center;
    transition-duration: .3s;
    display: flex
}

.letsTalkBtn-BG {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(150deg, #71cbff, #8000ff);
    z-index: -1;
    left: 6px;
    top: 6px;
    border-radius: 7px;
    pointer-events: none;
    transition: .3s
}

.letsTalkBtn:hover {
    transform: translateY(-2px) translateX(-2px)
}

.letsTalkBtn:hover .letsTalkBtn-BG {
    transform: translateY(2px) translateX(2px)
}

.letsTalkBtn:active {
    transform: translateY(7px) translateX(7px)
}

.letsTalkBtn:active .letsTalkBtn-BG {
    transform: translateY(-7px) translateX(-7px)
}

.resume-btn {
    margin-top: 25px;
    justify-content: flex-start;
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, .199);
    background: linear-gradient(82.3deg, #965de9 10.8%, #6358ee 94.3%);
    position: relative
}

.projects-heading-article,
.projectsHeadingP,
.section-heading-article,
.sectionHeadingP,
.skills-heading-article,
.skillsHeadingP {
    background-image: -webkit-gradient(linear, left top, right top, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink));
    background-image: -webkit-linear-gradient(left, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink))
}

.dp::after,
.dp::before,
.text {
    position: absolute
}

.sign {
    width: 100%;
    justify-content: center
}

.sign svg {
    width: 17px
}

.text {
    right: 0;
    width: 0%;
    opacity: 0;
    font-size: 1em;
    font-weight: 600;
    transition-duration: .3s
}

.resume-btn:hover {
    width: 130px;
    border-radius: 40px;
    transition-duration: .3s
}

.resume-btn:hover .sign {
    width: 30%;
    transition-duration: .3s;
    padding-left: 20px
}

.resume-btn:hover .text {
    opacity: 1;
    width: 70%;
    transition-duration: .3s;
    padding-right: 10px
}

.dp,
.dp::after,
.dp::before,
.dp:hover::after,
.dp:hover::before {
    transition-duration: .5s
}

.resume-btn:active {
    transform: translate(2px, 2px)
}

.about-section-container {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-top: 200px
}

.about-section,
.projects-section-div,
.skills-section {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.projects-heading,
.section-heading,
.skills-heading {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px
}

.dp img,
.dp::after,
.dp::before {
    width: 300px
}

.projects-heading-article,
.section-heading-article,
.skills-heading-article {
    font-size: 3rem;
    font-family: fira code;
    font-weight: 500;
    background-image: linear-gradient(90deg, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent
}

.projectsHeadingP,
.sectionHeadingP,
.skillsHeadingP {
    width: 300px;
    height: 1px;
    background-image: linear-gradient(90deg, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink))
}

.info-dp-section {
    width: 100%;
    display: flex;
    margin-top: 30px
}

.about-info {
    width: 60%;
    display: flex;
    flex-direction: column
}

.about-info p {
    line-height: 25px
}

.dp {
    width: 40%;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    position: relative;
    border-radius: 20px; /* Added border-radius */
}

.dp::before {
    box-shadow: 2px 2px 0 inset #8000ff, -2px -2px 0 inset transparent;
    border-radius: 30px; /* Added border-radius */
}

.dp::after {
    box-shadow: 2px 2px 0 inset transparent, -2px -2px 0 inset #8000ff;
    border-radius: 30px; /* Added border-radius */
}

.dp:hover::after {
    transform: translate(30px, 30px)
}

.dp:hover::before {
    transform: translate(-30px, -30px)
}

.skills-section-container {
    padding-top: 150px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center
}

/* Experience Section Styles */
.experience-section-container {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding-top: 150px;
}

.experience-section {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.experience-items {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 50px;
    margin-top: 50px;
}

.experience-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px;
    background-image: linear-gradient(to bottom right, var(--tech-stack-box-first-color), var(--tech-stack-box-second-color));
    border: 1px solid var(--tech-stack-box-border-color);
    border-radius: 30px;
    transition: transform 0.3s ease;
}

.experience-item:hover {
    transform: translateY(-5px);
}

.experience-content {
    width: 60%;
    padding-right: 30px;
}

.company-name {
    color: var(--color-white);
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.job-title {
    color: var(--color-light-purple);
    font-size: 2rem;
    font-style: italic;
    margin-bottom: 20px;
}

.job-responsibilities {
    list-style-type: none;
    padding: 0;
}

.job-responsibilities li {
    color: var(--color-ddd-color);
    font-size: 1.6rem;
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.job-responsibilities li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--color-light-purple);
}

.company-image {
    width: 40%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-container {
    width: 150px;
    height: 150px;
    aspect-ratio: 1;
    border-radius: 9999px;
    overflow: hidden;
    border: 2px solid var(--color-light-purple);
    background-color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 15px rgba(128, 0, 255, 0.2);
}

.image-container img {
    width: 85%;
    height: 85%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.image-container:hover img {
    transform: scale(1.15);
}

/* Media queries for experience section */
@media screen and (max-width: 768px) {
    .experience-item {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .experience-content {
        width: 100%;
        padding-right: 0;
    }

    .company-image {
        width: 100%;
    }

    .image-container {
        width: 100px;
        height: 100px;
        min-width: 100px;
        min-height: 100px;
        padding: 15px;
    }
}

@media screen and (max-width: 480px) {
    .image-container {
        width: 80px;
        height: 80px;
        min-width: 80px;
        min-height: 80px;
        padding: 12px;
    }
}

.language-speak,
.tech-stack-wrapper {
    width: 100%;
    display: flex;
    align-items: center
}

.language-speak {
    height: 50px;
    justify-content: center
}

.language-speak article {
    font-size: 2rem;
    font-weight: 600
}

.frontend-dev-section {
    width: 100%;
    margin-top: 50px
}

.frontend-dev-heading {
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 3rem;
    font-weight: 900;
    height: 100px;
    letter-spacing: 5px;
    background-image: -webkit-gradient(linear, left top, right top, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink));
    background-image: -webkit-linear-gradient(left, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink));
    background-image: linear-gradient(90deg, var(--static-heading-gradient-blue), var(--static-heading-gradient-pink));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent
}

.project-box,
.tech-stack-box {
    background-image: linear-gradient(to bottom right, var(--tech-stack-box-first-color), var(--tech-stack-box-second-color))
}

.tech-stack-wrapper {
    justify-content: center;
    flex-wrap: wrap;
    gap: 50px;
    margin-top: 50px
}

.tech-stack-box,
.tooltip {
    align-items: center;
    display: flex
}

.tech-stack-box {
    width: 160px;
    height: 160px;
    justify-content: center;
    border-radius: 30px;
    border: 1px solid var(--tech-stack-box-border-color);
    position: relative
}

.tech-stack-box img {
    width: 80%
}

.tech-stack-box:hover .tooltip {
    opacity: 1;
    transition-duration: .3s
}

.tooltip {
    position: absolute;
    top: -20px;
    opacity: 0;
    background: linear-gradient(to bottom right, var(--tech-stack-box-first-color), var(--tech-stack-box-second-color));
    border: 1px solid var(--tech-stack-box-border-color);
    padding: 5px 10px;
    border-radius: 5px;
    justify-content: center;
    transition-duration: .2s;
    pointer-events: none;
    letter-spacing: .5px;
    font-size: 1.6rem;
    backdrop-filter: blur(5px);
    font-weight: 500
}

.cta,
.cta span,
.cta svg,
.project-box,
footer {
    position: relative
}

.projects-section-container {
    padding: 150px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%
}

.project-boxes-div {
    width: 100%;
    height: fit-content;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 50px;
    padding: 50px
}

.cta:hover:before,
.project-box-wrapper {
    width: 100%
}

.project-box {
    width: 100%;
    height: 400px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    transition-duration: .3s;
    border-radius: 30px;
    border: 1px solid var(--tech-stack-box-border-color)
}

.info-div {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 20px;
    padding: 0 40px
}

.faviconforProject {
    width: 30px;
    border-radius: 50%
}

.faviconforProjectAquaregia {
    width: 20px
}

.image-div {
    width: 50%;
    height: 100%;
    padding-top: 70px;
    padding-left: 10px
}

.image-div img {
    height: 100%;
    border-top-left-radius: 30px
}

.ProjectHeading {
    color: var(--color-white);
    font-size: 3.4rem;
    font-weight: 700
}

.ProjectDescription {
    color: gray;
    font-size: 1.7rem;
    font-weight: 400
}

.project-buttons {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px
}

.github-redirect {
    width: 50px
}

.github-redirect img {
    width: 45px;
    height: auto;
    transition: .3s;
    filter: brightness(2)
}

.github-redirect:hover img {
    filter: brightness(4)
}

.cta {
    padding: 12px 18px;
    transition: .2s;
    border: none;
    background: 0 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start
}

.cta::before {
    content: "";
    position: absolute;
    left: 0;
    display: block;
    border-radius: 50px;
    background: #2b2f4e;
    width: 45px;
    height: 45px;
    transition: .3s
}

.cta span {
    font-size: 1.8rem;
    font-weight: 600;
    letter-spacing: .05em;
    color: var(--color-light-blue)
}

.cta svg {
    top: 0;
    margin-left: 10px;
    fill: none;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke: var(--color-light-blue);
    stroke-width: 2;
    transform: translateX(-5px);
    transition: .3s
}

.cta:hover svg {
    stroke: white;
    transform: translateX(0)
}

.cta:active {
    transform: scale(.95)
}

#backtotopbutton {
    position: fixed;
    right: 10px;
    bottom: 0;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: none;
    width: 3rem;
    height: 15rem;
    z-index: 101;
    text-align: center;
    transition: .4s;
    display: none
}

#backtotopbutton article {
    font-size: 1.5rem;
    color: var(--color-white);
    font-weight: 400;
    writing-mode: vertical-rl;
    mix-blend-mode: difference
}

#backtotopbutton:hover {
    transform: translate(0, -20px);
    text-decoration: line-through var(--color-white)
}

footer {
    width: 100%;
    height: fit-content;
    border-top: 1px solid var(--tech-stack-box-border-color)
}

.footer-background {
    width: 100%;
    height: 100vh;
    background-color: #0b0b0b;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 1
}

.footer-blob {
    width: 100%;
    height: 200px;
    background-color: var(--color-light-purple);
    border-radius: 50%;
    filter: blur(100px);
    opacity: .7
}

.footer-foreground {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    backdrop-filter: blur(10px)
}

.footercontainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end
}

.two-words {
    color: #b3b3b3;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px
}

.getintouch-heading,
.logos {
    height: 80px;
    width: 100%;
    display: flex
}

.two-words article {
    font-size: 3rem
}

.social-media-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%
}

.social-media-container a {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    padding: 10px;
    border-radius: 50%;
    border: 2px solid #e2dede;
    position: relative;
    box-shadow: 0 0 0 #8000ff inset;
    transition-duration: .3s
}

.getintouch-heading {
    align-items: center;
    justify-content: center
}

.getintouch-heading article {
    font-size: 2rem;
    background: linear-gradient(-45deg, var(--color-light-blue), var(--color-light-purple), var(--color-light-blue), var(--color-light-purple));
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: 700
}

.fa-copyright,
.footer-bottom article {
    font-size: 1.5rem;
    color: #b0b0b0
}

.logos {
    gap: 30px;
    justify-content: center;
    align-items: center
}

.SocialHandle {
    fill: white;
    width: 60%
}

#GmailLogo {
    height: 70%
}

.social-media-container a:hover {
    box-shadow: 0 0 50px 100px #8000ff inset;
    transition-duration: 1s;
    border: none
}

.social-media-container a::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    left: 0;
    border-radius: 50%;
    transform: scale(0);
    background-color: transparent;
    transition-duration: .3s;
    z-index: -1
}

.social-media-container a:hover::before {
    transform: scale(1);
    transition-duration: .3s
}

.footer-avatar-container {
    width: 220px;
    margin-top: 20px;
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center
}

.footer-avatar-img {
    width: 100%;
    z-index: 2
}

.footer-avatar-face {
    position: absolute;
    width: 100%;
    height: 97%;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 18px;
    padding-left: 5px
}

.footer-avatar-eye {
    width: 50px;
    height: 50px;
    background-color: #dfd2d2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center
}

.footer-pupil {
    width: 20px;
    height: 20px;
    background-color: #000;
    border-radius: 50%;
    border: 5px solid #413333;
    filter: blur(.6px)
}

.footer-right-eye {
    box-shadow: 15px 15px 5px rgba(0, 0, 0, .233) inset, 10px 10px 5px rgba(41, 33, 33, .644) inset;
    position: relative
}

.footer-left-eye {
    box-shadow: -10px -15px 5px rgba(0, 0, 0, .349) inset;
    position: relative
}

.footer-avatar-eye::before {
    position: absolute;
    content: "";
    width: 2px;
    height: 5px;
    border-radius: 50%;
    background-color: rgba(214, 214, 214, .726);
    z-index: 2;
    filter: blur(.2px);
    left: 15px;
    transform: rotate(45deg)
}

.footer-bottom {
    background-color: #0b0b0b;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;
    z-index: 3
}

.tonechange {
    color: #000
}

@media screen and (max-width:1920px) {
    html {
        font-size: 60%
    }
}

@media screen and (max-width:1400px) {
    html {
        font-size: 60%
    }
}

@media screen and (max-width:1200px) {
    html {
        font-size: 60%
    }

    .projects-section-div,
    .skills-section,
    .text-content {
        width: 80%
    }

    .about-section {
        width: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center
    }

    .logo:hover {
        transform: translateY(200px);
        transition-duration: 1s
    }
}

@media screen and (max-width:1150px) {
    html {
        font-size: 60%
    }
}

@media screen and (max-width:998px) {

    #hamburger-button,
    .hamburger {
        background-color: transparent
    }

    .mobile-navbar-tabs-ul a,
    .mobiletogglemenu {
        text-decoration: none
    }

    .activeThismobiletab,
    .mobile-navbar-tabs-ul a {
        color: var(--color-white)
    }

    html {
        font-size: 55%
    }

    .blob,
    .cursor-inner,
    .cursor-outer,
    .navbar-tabs-ul {
        display: none
    }

    .stopscrolling {
        overflow: hidden
    }

    .hamburger {
        position: fixed;
        top: 0;
        width: 100vw;
        height: 115px;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        z-index: 9998
    }

    .activeThismobiletab:before,
    .burger-bar,
    .hamburger-animation1,
    .hamburger-animation2,
    .hamburger-animation3 {
        background-color: var(--color-white)
    }

    .hamburgerbase {
        width: 80%;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px
    }

    #hamburger-button {
        font-size: 3rem;
        font-weight: 800;
        width: 4.5rem;
        height: 3.5rem;
        border: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between
    }

    #burger-bar1,
    #burger-bar2,
    #burger-bar3 {
        height: 3px;
        border-radius: 0;
        transition-duration: .3s
    }

    #burger-bar1,
    #burger-bar3 {
        width: 60%
    }

    #burger-bar2 {
        width: 100%
    }

    .hamburger-animation1 {
        transform: rotate(45deg) scaleX(1.7);
        transition-duration: .3s;
        transform-origin: left
    }

    .hamburger-animation2 {
        transform: scaleX(0)
    }

    .hamburger-animation3 {
        transform: rotateZ(-45deg) scaleX(1.7);
        transform-origin: left;
        transition-duration: .3s
    }

    .mobiletogglemenu {
        background-color: transparent;
        z-index: 9990;
        height: 100%;
        width: 100%;
        position: fixed;
        top: 0;
        right: 0;
        list-style: none;
        font-size: 2rem;
        cursor: pointer;
        display: flex;
        transform: translate(1000px);
        transition-duration: 1s
    }

    .show-toggle-menu {
        transform: translate(0);
        transition-duration: .6s
    }

    .mobile-navbar-tabs-ul {
        background-color: var(--color-dark-mode);
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 70px;
        box-shadow: 0 0 30px rgba(0, 0, 0, .176)
    }

    .mobile-navbar-tabs-li {
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        font-weight: 600;
        position: relative;
        cursor: pointer;
        font-family: fira code;
        height: 30px;
        width: fit-content
    }

    .about-info,
    .dp,
    .info-dp-section {
        width: 100%;
        display: flex
    }

    .info-dp-section {
        flex-direction: column;
        margin-top: 30px;
        gap: 30px
    }

    .about-info {
        flex-direction: column
    }

    .dp {
        align-items: center;
        justify-content: center
    }

    .activeThismobiletab:before {
        content: "";
        position: absolute;
        height: 10px;
        width: 10px;
        left: -20px;
        border-radius: 50%
    }

    .projects-section-div {
        width: 80%
    }

    .project-box {
        flex-direction: column;
        height: 800px
    }

    .image-div,
    .info-div {
        width: 100%;
        height: 50%
    }

    .image-div {
        padding: 0 0 0 80px
    }
}

@media screen and (max-width:768px) {
    html {
        font-size: 50%
    }

    .text-content {
        padding-left: 0;
        width: 80%
    }

    #hello-friend {
        height: 100px;
        font-size: 3.5rem
    }

    #name,
    #work {
        font-size: 5.5rem
    }

    #info-para,
    .language-name {
        width: 100%
    }

    .contact-btn-div {
        height: 140px;
        display: flex;
        flex-direction: column-reverse;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 20px
    }

    .setting-container {
        margin-left: 0
    }

    .dp::after,
    .dp::before {
        display: none
    }

    .logo {
        width: 18%
    }

    .logo:hover {
        transform: translateY(200px);
        transition-duration: 1s
    }

    .projects-section-div {
        width: 80%
    }

    .project-boxes-div {
        padding: 40px 0
    }
}

@media screen and (max-width:500px) {
    html {
        font-size: 45%
    }

    .hamburgerbase,
    .navbar {
        height: 70px
    }

    #hamburger {
        margin-right: 20px;
        height: 115px;
        align-items: flex-end
    }

    #hello-friend,
    .two-words article {
        font-size: 2.5rem
    }

    .hey {
        left: 70px
    }

    .logo:hover {
        transform: translateY(200px);
        transition-duration: 1s
    }

    .landing-page-container {
        height: 85vh
    }

    .text-content {
        justify-content: flex-start
    }

    .project-box {
        height: 550px
    }

    .info-div {
        height: 60%
    }

    .image-div {
        height: 40%;
        padding-left: 50px
    }

    .tech-stack-wrapper {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px
    }

    .tech-stack-box {
        width: 100%;
        height: calc(76vw/2)
    }
}

@media screen and (max-width:430px) {
    html {
        font-size: 45%
    }

    .about-section,
    .projects-section-div,
    .skills-section {
        width: 80%
    }

    .info-div {
        padding: 0 30px
    }

    .two-words article {
        font-size: 2.2rem
    }
}

@media screen and (max-width:375px) {
    html {
        font-size: 42%
    }

    .dp img {
        width: 200px
    }

    .two-words {
        height: 50px
    }

    .two-words article {
        font-size: 2rem
    }

    .footer-bottom article {
        font-size: 1.3rem;
        text-align: center;
    }

    .logos {
        gap: 20px
    }
}

@media screen and (max-width:300px) {
    html {
        font-size: 42%
    }

    .language-name article {
        text-align: center
    }

    .two-words {
        align-items: flex-end;
        height: 50px
    }

    .two-words article {
        font-size: 1.5rem
    }

    .footer-bottom article {
        font-size: 1rem
    }

    .social-media-container a {
        width: 50px;
        height: 50px
    }

    .logos {
        gap: 20px
    }
}


@media only screen and (max-width: 1300px) and (max-height: 730px){
    .landing-page-container {
        height: fit-content;
        margin-top: 100px;  
    }
}